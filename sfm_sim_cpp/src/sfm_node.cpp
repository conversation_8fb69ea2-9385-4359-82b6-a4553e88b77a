#include "rclcpp/rclcpp.hpp"
#include "geometry_msgs/msg/twist.hpp"
#include "turtlesim/msg/pose.hpp"
#include "turtlesim/srv/teleport_absolute.hpp"
#include <cmath>
#include <vector>

/**
 * @brief Social Force Model Node for TurtleSim
 *
 * This node implements the Social Force Model (SFM) which simulates realistic
 * movement behavior by combining multiple forces:
 * 1. Attractive force toward the goal
 * 2. Repulsive force from obstacles/other agents
 * 3. Wall repulsion forces from boundaries
 */
class SFMNode : public rclcpp::Node
{
public:
    SFMNode() : Node("sfm_node")
    {
        // Publishers and Subscribers
        cmd_pub_ = this->create_publisher<geometry_msgs::msg::Twist>("/turtle1/cmd_vel", 10);

        pose_sub_ = this->create_subscription<turtlesim::msg::Pose>(
            "/turtle1/pose", 10,
            std::bind(&SFMNode::turtle1_callback, this, std::placeholders::_1));

        obs_sub_ = this->create_subscription<turtlesim::msg::Pose>(
            "/turtle2/pose", 10,
            std::bind(&SFMNode::obstacle_callback, this, std::placeholders::_1));

        teleport_cli_ = this->create_client<turtlesim::srv::TeleportAbsolute>("/turtle1/teleport_absolute");

        // Initialize SFM parameters
        initialize_sfm_parameters();

        // Set goal and start positions
        goal_x_ = 8.0;
        goal_y_ = 8.0;
        start_x_ = 2.0;
        start_y_ = 2.0;

        resetting_ = false;

        RCLCPP_INFO(this->get_logger(), "SFM Node initialized. Goal: (%.1f, %.1f)", goal_x_, goal_y_);
    }

private:
    // Pose data
    turtlesim::msg::Pose::SharedPtr self_pose_;
    turtlesim::msg::Pose::SharedPtr obs_pose_;
    bool resetting_;

    // Goal and start positions
    double goal_x_, goal_y_;
    double start_x_, start_y_;

    // ROS2 communication
    rclcpp::Publisher<geometry_msgs::msg::Twist>::SharedPtr cmd_pub_;
    rclcpp::Subscription<turtlesim::msg::Pose>::SharedPtr pose_sub_, obs_sub_;
    rclcpp::Client<turtlesim::srv::TeleportAbsolute>::SharedPtr teleport_cli_;

    // === SOCIAL FORCE MODEL PARAMETERS ===

    // Attractive force parameters (toward goal)
    double A_goal_;          // Strength of goal attraction
    double B_goal_;          // Range of goal attraction

    // Repulsive force parameters (from obstacles)
    double A_obs_;           // Strength of obstacle repulsion
    double B_obs_;           // Range of obstacle repulsion
    double R_obs_;           // Obstacle radius

    // Wall repulsion parameters
    double A_wall_;          // Strength of wall repulsion
    double B_wall_;          // Range of wall repulsion

    // Physical parameters
    double max_speed_;       // Maximum desired speed
    double mass_;            // Agent mass (for realistic dynamics)
    double relaxation_time_; // Time to reach desired velocity

    // TurtleSim boundaries
    double world_min_x_, world_max_x_;
    double world_min_y_, world_max_y_;

    /**
     * @brief Initialize Social Force Model parameters
     * These parameters control the behavior of the SFM simulation
     */
    void initialize_sfm_parameters()
    {
        // Goal attraction parameters
        A_goal_ = 2.0;      // Moderate attraction strength
        B_goal_ = 1.0;      // Attraction range

        // Obstacle repulsion parameters
        A_obs_ = 5.0;       // Strong repulsion strength
        B_obs_ = 0.5;       // Short repulsion range
        R_obs_ = 0.3;       // Obstacle radius

        // Wall repulsion parameters
        A_wall_ = 3.0;      // Wall repulsion strength
        B_wall_ = 0.2;      // Wall repulsion range

        // Physical parameters
        max_speed_ = 2.0;   // Maximum speed
        mass_ = 1.0;        // Agent mass
        relaxation_time_ = 0.5; // Time to reach desired velocity

        // TurtleSim world boundaries (standard turtlesim is 11x11)
        world_min_x_ = 0.5;
        world_max_x_ = 10.5;
        world_min_y_ = 0.5;
        world_max_y_ = 10.5;

        RCLCPP_INFO(this->get_logger(), "SFM parameters initialized");
    }

    void turtle1_callback(const turtlesim::msg::Pose::SharedPtr msg)
    {
        self_pose_ = msg;
        if (obs_pose_) {  // Only update motion if we have obstacle data too
            update_motion();
        }
    }

    void obstacle_callback(const turtlesim::msg::Pose::SharedPtr msg)
    {
        obs_pose_ = msg;
        if (self_pose_) {  // Only update motion if we have self pose data too
            update_motion();
        }
    }

    /**
     * @brief Main Social Force Model computation
     * Calculates and applies forces according to SFM principles
     */
    void update_motion()
    {
        if (!self_pose_ || !obs_pose_ || resetting_) return;

        // === STEP 1: Calculate Goal Attractive Force ===
        auto [f_goal_x, f_goal_y] = calculate_goal_force();

        // === STEP 2: Calculate Obstacle Repulsive Force ===
        auto [f_obs_x, f_obs_y] = calculate_obstacle_force();

        // === STEP 3: Calculate Wall Repulsive Forces ===
        auto [f_wall_x, f_wall_y] = calculate_wall_forces();

        // === STEP 4: Combine All Forces ===
        double total_fx = f_goal_x + f_obs_x + f_wall_x;
        double total_fy = f_goal_y + f_obs_y + f_wall_y;

        // === STEP 5: Apply SFM Dynamics ===
        apply_social_force_dynamics(total_fx, total_fy);

        // Log forces for debugging (optional)
        if (static_cast<int>(this->now().seconds()) % 2 == 0) {  // Log every 2 seconds
            RCLCPP_DEBUG(this->get_logger(),
                "Forces - Goal:(%.2f,%.2f) Obs:(%.2f,%.2f) Wall:(%.2f,%.2f) Total:(%.2f,%.2f)",
                f_goal_x, f_goal_y, f_obs_x, f_obs_y, f_wall_x, f_wall_y, total_fx, total_fy);
        }
    }

    /**
     * @brief Calculate attractive force toward the goal
     * F_goal = A_goal * exp(-d/B_goal) * e_goal
     */
    std::pair<double, double> calculate_goal_force()
    {
        double dx = goal_x_ - self_pose_->x;
        double dy = goal_y_ - self_pose_->y;
        double dist_to_goal = std::hypot(dx, dy);

        // Check if goal is reached
        if (dist_to_goal < 0.2) {
            handle_goal_reached();
            return {0.0, 0.0};
        }

        // Normalize direction vector
        double e_x = dx / dist_to_goal;
        double e_y = dy / dist_to_goal;

        // Calculate attractive force magnitude
        double force_magnitude = A_goal_ * std::exp(-dist_to_goal / B_goal_);

        return {force_magnitude * e_x, force_magnitude * e_y};
    }

    /**
     * @brief Calculate repulsive force from obstacles
     * F_obs = A_obs * exp(-(d-R)/B_obs) * n_obs
     */
    std::pair<double, double> calculate_obstacle_force()
    {
        double dx = self_pose_->x - obs_pose_->x;
        double dy = self_pose_->y - obs_pose_->y;
        double dist_to_obs = std::hypot(dx, dy);

        // Avoid division by zero
        if (dist_to_obs < 0.01) {
            return {10.0, 10.0};  // Strong repulsion if too close
        }

        // Normalize direction vector (pointing away from obstacle)
        double n_x = dx / dist_to_obs;
        double n_y = dy / dist_to_obs;

        // Calculate repulsive force magnitude
        double effective_distance = dist_to_obs - R_obs_;
        double force_magnitude = A_obs_ * std::exp(-effective_distance / B_obs_);

        return {force_magnitude * n_x, force_magnitude * n_y};
    }

    /**
     * @brief Calculate repulsive forces from walls/boundaries
     * Prevents the turtle from going outside the world boundaries
     */
    std::pair<double, double> calculate_wall_forces()
    {
        double fx = 0.0, fy = 0.0;

        // Left wall
        double dist_left = self_pose_->x - world_min_x_;
        if (dist_left < 1.0) {
            fx += A_wall_ * std::exp(-dist_left / B_wall_);
        }

        // Right wall
        double dist_right = world_max_x_ - self_pose_->x;
        if (dist_right < 1.0) {
            fx -= A_wall_ * std::exp(-dist_right / B_wall_);
        }

        // Bottom wall
        double dist_bottom = self_pose_->y - world_min_y_;
        if (dist_bottom < 1.0) {
            fy += A_wall_ * std::exp(-dist_bottom / B_wall_);
        }

        // Top wall
        double dist_top = world_max_y_ - self_pose_->y;
        if (dist_top < 1.0) {
            fy -= A_wall_ * std::exp(-dist_top / B_wall_);
        }

        return {fx, fy};
    }

    /**
     * @brief Apply SFM dynamics to convert forces to velocity commands
     * Uses the relaxation time approach: dv/dt = (v_desired - v_current) / tau
     */
    void apply_social_force_dynamics(double fx, double fy)
    {
        // Calculate desired velocity from forces
        double desired_vx = fx / mass_;
        double desired_vy = fy / mass_;

        // Limit to maximum speed
        double desired_speed = std::hypot(desired_vx, desired_vy);
        if (desired_speed > max_speed_) {
            desired_vx = (desired_vx / desired_speed) * max_speed_;
            desired_vy = (desired_vy / desired_speed) * max_speed_;
        }

        // Calculate desired direction
        double desired_theta = std::atan2(desired_vy, desired_vx);
        double angle_diff = desired_theta - self_pose_->theta;

        // Normalize angle difference
        while (angle_diff > M_PI) angle_diff -= 2 * M_PI;
        while (angle_diff < -M_PI) angle_diff += 2 * M_PI;

        // Create velocity command
        geometry_msgs::msg::Twist cmd;

        // Angular velocity to align with desired direction
        cmd.angular.z = 3.0 * angle_diff;

        // Linear velocity (only move forward when roughly aligned)
        if (std::abs(angle_diff) < 0.5) {  // Within 30 degrees
            cmd.linear.x = std::hypot(desired_vx, desired_vy);
        } else {
            cmd.linear.x = 0.1;  // Slow movement while turning
        }

        // Limit velocities
        cmd.linear.x = std::min(cmd.linear.x, max_speed_);
        cmd.angular.z = std::max(-5.0, std::min(5.0, cmd.angular.z));

        cmd_pub_->publish(cmd);
    }

    /**
     * @brief Handle goal reached event
     */
    void handle_goal_reached()
    {
        geometry_msgs::msg::Twist cmd;
        cmd.linear.x = 0.0;
        cmd.angular.z = 0.0;
        cmd_pub_->publish(cmd);

        resetting_ = true;
        RCLCPP_INFO(this->get_logger(), "Goal reached! Resetting to start position...");

        // Small delay before reset
        rclcpp::sleep_for(std::chrono::milliseconds(1000));
        teleport_to_start();
    }

    void teleport_to_start()
    {
        if (!teleport_cli_->wait_for_service(std::chrono::seconds(1)))
        {
            RCLCPP_WARN(this->get_logger(), "Teleport service not available.");
            resetting_ = false;
            return;
        }

        auto req = std::make_shared<turtlesim::srv::TeleportAbsolute::Request>();
        req->x = start_x_;
        req->y = start_y_;
        req->theta = 0.0;

        auto future = teleport_cli_->async_send_request(req);
        future.wait();

        RCLCPP_INFO(this->get_logger(), "Turtle reset. Starting again.");
        resetting_ = false;
    }
};

/**
 * @brief Main function - Entry point for the SFM node
 */
int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);

    RCLCPP_INFO(rclcpp::get_logger("main"), "Starting Social Force Model Node...");
    RCLCPP_INFO(rclcpp::get_logger("main"), "Make sure to run: ros2 run turtlesim turtlesim_node");
    RCLCPP_INFO(rclcpp::get_logger("main"), "And spawn a second turtle: ros2 service call /spawn turtlesim/srv/Spawn \"{x: 6, y: 6, theta: 0, name: 'turtle2'}\"");

    auto node = std::make_shared<SFMNode>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
