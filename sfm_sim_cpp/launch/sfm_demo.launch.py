#!/usr/bin/env python3

"""
Social Force Model Demo Launch File

This launch file starts:
1. TurtleSim node
2. Spawns a second turtle as an obstacle
3. Starts the SFM node

Usage: ros2 launch sfm_sim_cpp sfm_demo.launch.py
"""

from launch import LaunchDescription
from launch.actions import ExecuteProcess, TimerAction
from launch_ros.actions import Node
import time

def generate_launch_description():
    return LaunchDescription([
        # Start turtlesim node
        Node(
            package='turtlesim',
            executable='turtlesim_node',
            name='turtlesim',
            output='screen'
        ),
        
        # Wait a bit for turtlesim to start, then spawn second turtle
        TimerAction(
            period=2.0,
            actions=[
                ExecuteProcess(
                    cmd=[
                        'ros2', 'service', 'call', '/spawn',
                        'turtlesim/srv/Spawn',
                        '"{x: 6.0, y: 6.0, theta: 0.0, name: turtle2}"'
                    ],
                    output='screen'
                )
            ]
        ),
        
        # Wait a bit more, then start SFM node
        TimerAction(
            period=4.0,
            actions=[
                Node(
                    package='sfm_sim_cpp',
                    executable='sfm_node',
                    name='sfm_node',
                    output='screen'
                )
            ]
        ),
    ])
